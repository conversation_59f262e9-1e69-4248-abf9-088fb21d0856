/* Main contact page layout */
.app__contactpage {
  min-height: 100vh;
  background: var(--color-black);
  color: #fff;
  padding: 0;
}

/* Header Section */
.contact-header {
  padding: 4rem 2rem 2rem;
  text-align: left;
  max-width: 1200px;
  margin: 0 auto;
}

.contact-main-title {
  font-family: var(--font-base);
  font-size: 4rem;
  font-weight: 400;
  color: #fff;
  margin-bottom: 2rem;
  line-height: 1.1;
}

.contact-main-description {
  font-family: var(--font-alt);
  font-size: 1.1rem;
  line-height: 1.6;
  color: #ccc;
  max-width: 600px;
}

/* Main Content Layout */
.contact-main-content {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 4rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  align-items: start;
}

/* Contact Form Section */
.contact-form-section {
  max-width: 350px;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-family: var(--font-alt);
  font-size: 0.9rem;
  color: #fff;
  margin-bottom: 0.5rem;
  font-weight: 400;
}

.form-group input,
.form-group textarea {
  background: transparent;
  border: none;
  border-bottom: 1px solid #555;
  color: #fff;
  padding: 0.8rem 0;
  font-family: var(--font-alt);
  font-size: 0.9rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-bottom-color: var(--color-golden);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: #666;
  font-size: 0.85rem;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.submit-btn {
  background: var(--color-golden);
  color: var(--color-black);
  border: none;
  padding: 0.8rem 2rem;
  font-family: var(--font-alt);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  align-self: flex-start;
}

.submit-btn:hover {
  background: transparent;
  color: var(--color-golden);
  border: 1px solid var(--color-golden);
}

.form-success {
  background: rgba(20, 50, 20, 0.5);
  border: 1px solid rgba(50, 220, 50, 0.3);
  padding: 1.5rem;
  border-radius: 4px;
  text-align: center;
}

/* Google Maps Section */
.map-section {
  display: flex;
  justify-content: center;
  align-items: center;
}

.map-container {
  background: var(--color-black);
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  width: 100%;
  max-width: 600px;
  position: relative;
  border: 3px solid var(--color-golden);
  box-shadow:
    0 0 20px rgba(212, 175, 55, 0.3),
    inset 0 0 20px rgba(212, 175, 55, 0.1);
}

.map-container::before {
  content: '';
  position: absolute;
  top: -8px;
  left: -8px;
  right: -8px;
  bottom: -8px;
  background: linear-gradient(45deg, var(--color-golden), transparent, var(--color-golden));
  border-radius: 12px;
  z-index: -1;
  opacity: 0.6;
}

.map-header {
  margin-bottom: 1rem;
}

.map-header h2 {
  font-family: var(--font-base);
  font-size: 1.8rem;
  color: var(--color-golden);
  margin-bottom: 0.5rem;
  font-weight: 400;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.map-header p {
  font-family: var(--font-alt);
  font-size: 0.95rem;
  color: #fff;
  margin: 0;
  opacity: 0.9;
}

.map-frame {
  position: relative;
  border-radius: 6px;
  overflow: hidden;
  margin: 1rem 0;
  border: 2px solid var(--color-golden);
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.3),
    0 0 10px rgba(212, 175, 55, 0.4);
}

.map-frame::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(212, 175, 55, 0.1) 0%,
    transparent 50%,
    rgba(212, 175, 55, 0.1) 100%
  );
  pointer-events: none;
  z-index: 1;
}

.map-frame iframe {
  display: block;
  filter: sepia(10%) saturate(120%) brightness(95%);
  transition: filter 0.3s ease;
}

.map-frame:hover iframe {
  filter: sepia(0%) saturate(100%) brightness(100%);
}

.map-footer {
  margin-top: 1rem;
}

.map-footer p {
  font-family: var(--font-alt);
  font-size: 0.9rem;
  color: #ccc;
  margin: 0;
  opacity: 0.8;
}





/* Responsive Styles */
@media screen and (max-width: 1024px) {
  .contact-main-content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .contact-form-section {
    max-width: 100%;
  }

  .map-section {
    order: -1;
  }

  .map-container {
    min-width: auto;
    max-width: 100%;
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
  }
}

@media screen and (max-width: 768px) {
  .contact-header {
    padding: 3rem 1rem 2rem;
    text-align: center;
  }

  .contact-main-title {
    font-size: 3rem;
  }

  .contact-main-content {
    padding: 1rem;
    gap: 2rem;
  }

  .map-container {
    padding: 1.5rem 1rem;
    min-width: auto;
    width: 100%;
    max-width: 350px;
    margin: 0 auto;
  }

  .map-frame {
    margin: 0.8rem 0;
  }


}

@media screen and (max-width: 576px) {
  .contact-main-title {
    font-size: 2.5rem;
  }

  .contact-main-description {
    font-size: 1rem;
  }

  .contact-main-content {
    gap: 1.5rem;
  }

  .map-container {
    padding: 1.2rem 0.8rem;
  }

  .map-header h2 {
    font-size: 1.5rem;
    margin-bottom: 0.4rem;
  }

  .map-header p {
    font-size: 0.85rem;
  }

  .map-frame {
    margin: 0.6rem 0;
  }

  .contact-form-section {
    max-width: 100%;
  }
}