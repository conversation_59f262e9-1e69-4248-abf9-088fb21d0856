"use client";

import React from "react";
import Image from "next/image";
import { images } from "@/constants";

const AboutHistory = () => {
  return (
    <div className="app__aboutpage-history">
      <div className="app__aboutpage-history_image">
        <Image 
          src={images.G} 
          alt="G letter" 
          width={300}
          height={320}
          className="app__aboutpage-history_overlay"
        />
        <Image 
          src={images.findus} 
          alt="Restaurant interior" 
          width={500}
          height={650}
          priority
        />
      </div>
      <div className="app__aboutpage-history_text">
        <h2 className="headtext__cormorant">Our Journey</h2>
        <div className="app__aboutpage-history_spoon">
          <Image src={images.spoon} alt="spoon" width={45} height={15} />
        </div>
        <p className="p__opensans">
          Founded in 2018, BLUBRASSERIE emerged from a passion for exceptional dining and culinary artistry. What began as a small family restaurant has evolved into one of the city&apos;s most celebrated fine dining establishments, honored with multiple awards for our innovative approach to modern cuisine with traditional roots.
        </p>
        <p className="p__opensans">
          Our journey has been guided by a commitment to authenticity, quality, and creating memorable experiences for our guests. Over the years, we&apos;ve refined our craft while staying true to our founding principles—sourcing the finest ingredients, honoring culinary traditions, and delivering impeccable service in an atmosphere of sophisticated comfort.
        </p>
        <p className="p__opensans">
          Each dish at BLUBRASSERIE tells a story—of heritage, of innovation, and of our continuous pursuit of culinary excellence. As we look to the future, we remain dedicated to pushing boundaries while honoring the timeless art of hospitality that has defined us from the beginning.
        </p>
      </div>
    </div>
  );
};

export default AboutHistory;
