import images from './images';
import data from './data';

// Single format references for backward compatibility
const meal = 'https://res.cloudinary.com/dviwkw86f/video/upload/v1748756003/indoor_wyj3ga.mp4';
const herovideo = 'https://res.cloudinary.com/dviwkw86f/video/upload/v1748756008/herovideo_s5rjoj.mp4';

// Enhanced multi-format references with multiple formats and quality options for maximum compatibility
const mealFormats = {
  // Primary formats in order of preference
  mp4: 'https://res.cloudinary.com/dviwkw86f/video/upload/v1748756003/indoor_wyj3ga.mp4',
  webm: 'https://res.cloudinary.com/dviwkw86f/video/upload/v1748756006/indoor_hjbfpv.webm',

  // Quality variants for different devices/connections
  mp4_low: 'https://res.cloudinary.com/dviwkw86f/video/upload/q_auto:low,w_640/v1748756003/indoor_wyj3ga.mp4',
  mp4_medium: 'https://res.cloudinary.com/dviwkw86f/video/upload/q_auto:good,w_1280/v1748756003/indoor_wyj3ga.mp4',

  // Poster image fallback
  poster: 'https://res.cloudinary.com/dviwkw86f/image/upload/v1748756003/indoor_poster.jpg'
};

const herovideoFormats = {
  // Primary formats in order of preference
  mp4: 'https://res.cloudinary.com/dviwkw86f/video/upload/v1748756008/herovideo_s5rjoj.mp4',
  webm: 'https://res.cloudinary.com/dviwkw86f/video/upload/v1748756002/herovideo_z8q8hv.webm',

  // Quality variants for different devices/connections
  mp4_low: 'https://res.cloudinary.com/dviwkw86f/video/upload/q_auto:low,w_640/v1748756008/herovideo_s5rjoj.mp4',
  mp4_medium: 'https://res.cloudinary.com/dviwkw86f/video/upload/q_auto:good,w_1280/v1748756008/herovideo_s5rjoj.mp4',

  // Poster image fallback
  poster: 'https://res.cloudinary.com/dviwkw86f/image/upload/v1748756008/herovideo_poster.jpg'
};

// Device and browser compatibility detection utilities
const videoCompatibility = {
  // Check if browser supports specific video format
  supportsFormat: (format) => {
    if (typeof window === 'undefined') return false;
    const video = document.createElement('video');
    return video.canPlayType(`video/${format}`) !== '';
  },

  // Get best supported format for current device
  getBestFormat: (formats) => {
    if (typeof window === 'undefined') return formats.mp4 || Object.values(formats)[0];

    const video = document.createElement('video');

    // Check WebM support first (usually better compression)
    if (formats.webm && video.canPlayType('video/webm') !== '') {
      return formats.webm;
    }

    // Fall back to MP4
    if (formats.mp4 && video.canPlayType('video/mp4') !== '') {
      return formats.mp4;
    }

    // Return first available format as last resort
    return Object.values(formats).find(url => typeof url === 'string') || '';
  },

  // Detect if device is likely to have video playback issues
  isLowPowerDevice: () => {
    if (typeof window === 'undefined') return false;

    const userAgent = window.navigator.userAgent.toLowerCase();
    const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;

    // Check for older devices or slow connections
    return (
      /android 4|android 5/.test(userAgent) ||
      /iphone os [5-9]/.test(userAgent) ||
      (connection && connection.effectiveType && ['slow-2g', '2g'].includes(connection.effectiveType))
    );
  }
};

export {
  images,
  meal,
  data,
  herovideo,
  mealFormats,
  herovideoFormats,
  videoCompatibility
};
