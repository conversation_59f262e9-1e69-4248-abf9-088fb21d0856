"use client";

import React from "react";
import Image from "next/image";
import { images } from "@/constants";

const AboutValues = () => {
  return (
    <div className="app__aboutpage-values">
      <h2 className="headtext__cormorant">Our Inspiration</h2>
      <div className="app__aboutpage-values_spoon">
        <Image src={images.spoon} alt="spoon" width={45} height={15} />
      </div>
      <div className="app__aboutpage-values_cards">
        <div className="app__aboutpage-values_card" data-card="seasonal">
          <div className="app__aboutpage-values_card-number">01</div>
          <div className="app__aboutpage-values_card-content">
            <h3 className="p__cormorant">Seasonal Rhythms</h3>
            <div className="app__aboutpage-values_card-divider"></div>
            <p className="p__opensans">
              Nature's ever-changing seasons guide our culinary calendar. From spring's tender greens to autumn's rich harvests, we draw inspiration from the earth's natural cycles, crafting menus that celebrate each moment's unique bounty and fleeting beauty.
            </p>
          </div>
          <div className="app__aboutpage-values_card-accent"></div>
        </div>
        <div className="app__aboutpage-values_card" data-card="heritage">
          <div className="app__aboutpage-values_card-number">02</div>
          <div className="app__aboutpage-values_card-content">
            <h3 className="p__cormorant">Cultural Heritage</h3>
            <div className="app__aboutpage-values_card-divider"></div>
            <p className="p__opensans">
              Our kitchen is a crossroads where culinary traditions from across the globe converge. We honor the wisdom of ancient techniques while embracing the stories and flavors that connect us to diverse cultures and their timeless gastronomic legacies.
            </p>
          </div>
          <div className="app__aboutpage-values_card-accent"></div>
        </div>
        <div className="app__aboutpage-values_card" data-card="artistic">
          <div className="app__aboutpage-values_card-number">03</div>
          <div className="app__aboutpage-values_card-content">
            <h3 className="p__cormorant">Artistic Expression</h3>
            <div className="app__aboutpage-values_card-divider"></div>
            <p className="p__opensans">
              Every plate is our canvas, every ingredient our paint. We find inspiration in the interplay of colors, textures, and forms—transforming simple elements into edible masterpieces that speak to both the palate and the soul through visual poetry.
            </p>
          </div>
          <div className="app__aboutpage-values_card-accent"></div>
        </div>
        <div className="app__aboutpage-values_card" data-card="moments">
          <div className="app__aboutpage-values_card-number">04</div>
          <div className="app__aboutpage-values_card-content">
            <h3 className="p__cormorant">Memorable Moments</h3>
            <div className="app__aboutpage-values_card-divider"></div>
            <p className="p__opensans">
              Life's most precious memories are often born around the table. We're inspired by the power of shared meals to create connections, celebrate milestones, and weave the golden threads that bind us together in the tapestry of human experience.
            </p>
          </div>
          <div className="app__aboutpage-values_card-accent"></div>
        </div>
      </div>
    </div>
  );
};

export default AboutValues;
