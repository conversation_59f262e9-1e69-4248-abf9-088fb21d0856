"use client";

import React from "react";
import Image from "next/image";
import { images } from "@/constants";

const AboutTeam = () => {
  return (
    <div className="app__aboutpage-team">
      <h2 className="headtext__cormorant">Meet Our Team</h2>
      <div className="app__aboutpage-team_spoon">
        <Image src={images.spoon} alt="spoon" width={45} height={15} />
      </div>
      <div className="app__aboutpage-team_content">
        <div className="app__aboutpage-team_image">
          <Image 
            src={images.chef} 
            alt="Our Chef" 
            width={600}
            height={600}
          />
        </div>
        <div className="app__aboutpage-team_text">
          <h3 className="p__cormorant"><PERSON></h3>
          <h4 className="p__cormorant" style={{ color: 'var(--color-golden)' }}>Executive Chef & Founder</h4>
          <p className="p__opensans">
            With over 20 years of culinary experience in renowned restaurants across Europe and Asia, Chef <PERSON> brings a unique perspective to BLUBRASSERIE&apos;s kitchen. His innovative approach combines classical training with a deep appreciation for global flavors and techniques.
          </p>
          <p className="p__opensans">
            Chef Lu<PERSON>&apos;s philosophy centers on respecting ingredients, honoring traditions, and creating dishes that evoke emotion. Under his leadership, our culinary team continues to push boundaries while maintaining the highest standards of excellence.
          </p>
          <p className="p__opensans">
            &quot;Cooking is about passion, about creating memories through flavors. At BLUBRASSERIE, we don&apos;t just serve food—we craft experiences that linger long after the last bite.&quot;
          </p>
          <p className="p__opensans" style={{ fontStyle: 'italic', color: 'var(--color-grey)' }}>— Kevin Luo</p>
        </div>
      </div>
    </div>
  );
};

export default AboutTeam;
