.app__aboutpage {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: rgba(15, 40, 70, 0.95);
  /* Fix horizontal scroll by containing all content */
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
}

.app__aboutpage-heading {
  text-align: center;
  margin-bottom: 4rem;
}

.app__aboutpage-back-link {
  margin-top: 1rem;
}

.app__aboutpage-back-link a {
  color: var(--color-golden);
  transition: color 0.3s ease;
  display: inline-flex;
  align-items: center;
}

.app__aboutpage-back-link a:hover {
  color: var(--color-white);
}

.app__aboutpage-content {
  display: flex;
  flex-direction: column;
  gap: 6rem;
  /* Fix horizontal scroll by ensuring content stays within bounds */
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* History Section */
.app__aboutpage-history {
  display: flex;
  gap: 4rem;
  align-items: center;
}

.app__aboutpage-history_image {
  flex: 1;
  position: relative;
  min-width: 300px;
}

.app__aboutpage-history_overlay {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 0;
  opacity: 0.2;
}

.app__aboutpage-history_image img:nth-child(2) {
  width: 100%;
  height: auto;
  border-radius: 4px;
  position: relative;
  z-index: 1;
  opacity: 0.9;
}

.app__aboutpage-history_text {
  flex: 1.2;
}

.app__aboutpage-history_text h2,
.app__aboutpage-values h2,
.app__aboutpage-team h2,
.app__aboutpage-visit h2 {
  color: var(--color-golden);
  margin-bottom: 1rem;
}

.app__aboutpage-history_spoon,
.app__aboutpage-values_spoon,
.app__aboutpage-team_spoon,
.app__aboutpage-visit_spoon {
  margin-bottom: 2rem;
}

.app__aboutpage-history_text p {
  margin: 1.5rem 0;
  color: var(--color-grey);
  line-height: 1.8;
}

/* Values Section */
.app__aboutpage-values {
  text-align: center;
  position: relative;
  /* Fix horizontal scroll by containing overflow */
  overflow: hidden;
  width: 100%;
}

.app__aboutpage-values::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  /* Reduced from 120% to prevent horizontal overflow */
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at center, rgba(220, 202, 135, 0.03) 0%, transparent 70%);
  pointer-events: none;
  z-index: 0;
}

.app__aboutpage-values > * {
  position: relative;
  z-index: 1;
}

.app__aboutpage-values_cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 3rem 2rem;
  margin-top: 3rem;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  /* Fix horizontal scroll by ensuring proper containment */
  width: 100%;
  box-sizing: border-box;
  padding: 0 1rem;
}

.app__aboutpage-values_card {
  background: linear-gradient(135deg, rgba(15, 40, 70, 0.8) 0%, rgba(15, 40, 70, 0.6) 100%);
  border: none;
  border-radius: 0;
  position: relative;
  overflow: hidden;
  transition: all 0.6s cubic-bezier(0.23, 1, 0.320, 1);
  backdrop-filter: blur(10px);
  min-height: 320px;
  display: flex;
  flex-direction: column;
  /* Fix horizontal scroll by ensuring cards stay within bounds */
  width: 100%;
  box-sizing: border-box;
}

/* Alternating card orientations - reduced rotation to prevent overflow */
.app__aboutpage-values_card:nth-child(odd) {
  transform: perspective(1000px) rotateY(-1deg) rotateX(0.5deg);
  margin-top: 0;
}

.app__aboutpage-values_card:nth-child(even) {
  transform: perspective(1000px) rotateY(1deg) rotateX(-0.5deg);
  margin-top: 2rem;
}

/* Sophisticated border treatment */
.app__aboutpage-values_card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    transparent 0%,
    rgba(220, 202, 135, 0.1) 25%,
    transparent 50%,
    rgba(220, 202, 135, 0.1) 75%,
    transparent 100%);
  background-size: 40px 40px;
  opacity: 0;
  transition: opacity 0.6s ease;
  pointer-events: none;
}

.app__aboutpage-values_card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    rgba(220, 202, 135, 0.2) 0%,
    transparent 20%,
    transparent 80%,
    rgba(220, 202, 135, 0.2) 100%);
  opacity: 0;
  transition: opacity 0.6s ease;
  pointer-events: none;
}

.app__aboutpage-values_card:hover {
  transform: perspective(1000px) rotateY(0deg) rotateX(0deg) translateY(-12px) scale(1.02);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(220, 202, 135, 0.3),
    inset 0 1px 0 rgba(220, 202, 135, 0.1);
  background: linear-gradient(135deg, rgba(15, 40, 70, 0.95) 0%, rgba(15, 40, 70, 0.8) 100%);
}

.app__aboutpage-values_card:hover::before,
.app__aboutpage-values_card:hover::after {
  opacity: 1;
}

/* Card number styling */
.app__aboutpage-values_card-number {
  position: absolute;
  top: 15px;
  right: 25px;
  font-family: 'Cormorant Upright', serif;
  font-size: 3.5rem;
  font-weight: 300;
  color: rgba(220, 202, 135, 0.12);
  line-height: 1;
  transition: all 0.6s ease;
  z-index: 1;
  user-select: none;
}

.app__aboutpage-values_card:hover .app__aboutpage-values_card-number {
  color: rgba(220, 202, 135, 0.2);
  transform: scale(1.05) translateY(-2px);
}

/* Card content */
.app__aboutpage-values_card-content {
  padding: 2.5rem 2rem 2rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
}

.app__aboutpage-values_card h3 {
  color: var(--color-golden);
  margin-bottom: 0.5rem;
  font-size: 26px;
  font-weight: 400;
  letter-spacing: 0.5px;
  position: relative;
}

/* Elegant divider */
.app__aboutpage-values_card-divider {
  width: 60px;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--color-golden), transparent);
  margin: 1rem 0 1.5rem;
  position: relative;
}

.app__aboutpage-values_card-divider::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 3px;
  height: 3px;
  background: var(--color-golden);
  border-radius: 50%;
}

.app__aboutpage-values_card p {
  color: var(--color-grey);
  line-height: 1.8;
  font-size: 15px;
  text-align: left;
  flex: 1;
  margin: 0;
}

/* Accent element */
.app__aboutpage-values_card-accent {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg,
    transparent 0%,
    var(--color-golden) 50%,
    transparent 100%);
  transform: scaleX(0);
  transition: transform 0.6s ease;
}

.app__aboutpage-values_card:hover .app__aboutpage-values_card-accent {
  transform: scaleX(1);
}

/* Team Section */
.app__aboutpage-team {
  text-align: center;
}

.app__aboutpage-team_content {
  display: flex;
  gap: 4rem;
  align-items: center;
  margin-top: 2rem;
}

.app__aboutpage-team_image {
  flex: 1;
  min-width: 280px;
}

.app__aboutpage-team_image img {
  width: 100%;
  height: auto;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.app__aboutpage-team_text {
  flex: 1.5;
  text-align: left;
}

.app__aboutpage-team_text h3 {
  font-size: 32px;
  margin-bottom: 0.5rem;
}

.app__aboutpage-team_text h4 {
  margin-bottom: 1.5rem;
  font-size: 20px;
}

.app__aboutpage-team_text p {
  margin-bottom: 1.5rem;
  color: var(--color-grey);
  line-height: 1.8;
}

/* Visit Section */
.app__aboutpage-visit {
  text-align: center;
}

.app__aboutpage-visit_content {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 2rem;
}

.app__aboutpage-visit_info {
  margin-bottom: 3rem;
}

.app__aboutpage-visit_info h3 {
  color: var(--color-golden);
  margin: 2rem 0 1rem 0;
  font-size: 23px;
}

.app__aboutpage-visit_info h3:first-child {
  margin-top: 0;
}

.app__aboutpage-visit_info p {
  color: var(--color-grey);
  margin: 0.5rem 0;
}

.app__aboutpage-visit_buttons {
  display: flex;
  gap: 2rem;
}

.app__aboutpage-visit_buttons .custom__button {
  background-color: var(--color-crimson);
  color: var(--color-black);
  padding: 0.75rem 2rem;
  border-radius: 1px;
  transition: all 0.3s ease;
  border: 1px solid var(--color-golden);
  cursor: pointer;
  font-weight: 700;
  letter-spacing: 0.04em;
  line-height: 28px;
  font-size: 16px;
}

.app__aboutpage-visit_buttons .custom__button:hover {
  background-color: transparent;
  color: var(--color-golden);
}

/* Responsive Styles */
@media screen and (max-width: 1150px) {
  .app__aboutpage-history,
  .app__aboutpage-team_content {
    flex-direction: column;
    gap: 3rem;
  }
  
  .app__aboutpage-history_text,
  .app__aboutpage-team_text {
    text-align: center;
  }
  
  .app__aboutpage-history_image,
  .app__aboutpage-team_image {
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
  }
}

@media screen and (max-width: 1150px) {
  .app__aboutpage-values_cards {
    grid-template-columns: 1fr;
    gap: 2rem;
    max-width: 600px;
    /* Ensure proper containment on smaller screens */
    padding: 0 2rem;
  }

  .app__aboutpage-values_card:nth-child(odd),
  .app__aboutpage-values_card:nth-child(even) {
    transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
    margin-top: 0;
  }
}

@media screen and (max-width: 850px) {
  .app__aboutpage-values_cards {
    gap: 1.5rem;
    margin-top: 2rem;
    /* Ensure proper containment on mobile */
    padding: 0 1.5rem;
  }

  .app__aboutpage-values_card {
    min-height: 280px;
  }

  .app__aboutpage-values_card-content {
    padding: 2rem 1.5rem 1.5rem;
  }

  .app__aboutpage-values_card-number {
    font-size: 3rem;
    top: 10px;
    right: 20px;
  }

  .app__aboutpage-visit_buttons {
    flex-direction: column;
    gap: 1rem;
  }
}

@media screen and (max-width: 650px) {
  .app__aboutpage-content {
    gap: 4rem;
  }

  .app__aboutpage-values_cards {
    gap: 1rem;
    /* Ensure proper containment on very small screens */
    padding: 0 1rem;
  }

  .app__aboutpage-values_card {
    min-height: 250px;
  }

  .app__aboutpage-values_card-content {
    padding: 1.5rem 1rem 1rem;
  }

  .app__aboutpage-values_card h3 {
    font-size: 22px;
  }

  .app__aboutpage-values_card p {
    font-size: 14px;
    line-height: 1.7;
  }

  .app__aboutpage-values_card-number {
    font-size: 2.5rem;
    top: 8px;
    right: 15px;
  }
}

.app__bg {
  background: url('/assets/bgwhiteblue.png');
  background-position: center;
  background-size: cover;
  background-repeat: repeat;
  background-attachment: fixed;
  background-color: var(--color-black);
} 